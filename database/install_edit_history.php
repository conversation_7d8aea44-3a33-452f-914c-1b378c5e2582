<?php
/**
 * ไฟล์สำหรับติดตั้งระบบประวัติการแก้ไข
 * รันไฟล์นี้เพื่อสร้างตารางและข้อมูลที่จำเป็นสำหรับ edit_job.php
 */

require_once '../includes/db.php';

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

echo "<h2>Installing Edit History System...</h2>";

try {
    // เริ่ม transaction
    $pdo->beginTransaction();
    
    // สร้างตาราง job_edit_history
    echo "<p>Creating job_edit_history table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS job_edit_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            job_id INT NOT NULL,
            user_id INT NOT NULL,
            username VARCHAR(50) NOT NULL,
            field_name VARCHAR(100) NOT NULL,
            old_value TEXT,
            new_value TEXT,
            edit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
            <PERSON>OREIG<PERSON> KEY (user_id) REFERENCES users(id),
            INDEX idx_job_id (job_id),
            INDEX idx_user_id (user_id),
            INDEX idx_timestamp (edit_timestamp)
        )
    ");
    
    // สร้างตาราง return_users
    echo "<p>Creating return_users table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS return_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL
        )
    ");
    
    // ตรวจสอบว่ามีตาราง depositor_names หรือไม่
    $result = $pdo->query("SHOW TABLES LIKE 'depositor_names'");
    if ($result->rowCount() == 0) {
        echo "<p>Creating depositor_names table...</p>";
        $pdo->exec("
            CREATE TABLE depositor_names (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL
            )
        ");
    }
    
    // เพิ่มข้อมูลตัวอย่าง
    echo "<p>Adding sample data...</p>";
    
    // ข้อมูลตัวอย่างสำหรับ return_users
    $return_users = ['Admin', 'User1', 'User2', 'พนักงานคลัง', 'หัวหน้าแผนก'];
    foreach ($return_users as $user) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO return_users (name) VALUES (?)");
        $stmt->execute([$user]);
    }
    
    // ข้อมูลตัวอย่างสำหรับ depositor_names
    $depositor_names = [
        'บริษัท ABC จำกัด',
        'บริษัท XYZ จำกัด', 
        'คุณสมชาย',
        'คุณสมหญิง',
        'บริษัท ขนส่งไทย จำกัด',
        'บริษัท โลจิสติกส์ จำกัด'
    ];
    foreach ($depositor_names as $name) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO depositor_names (name) VALUES (?)");
        $stmt->execute([$name]);
    }
    
    // สร้าง view สำหรับสรุปการแก้ไข
    echo "<p>Creating job_edit_summary view...</p>";
    $pdo->exec("
        CREATE OR REPLACE VIEW job_edit_summary AS
        SELECT 
            j.id as job_id,
            j.job_number,
            j.customer_name,
            COUNT(jeh.id) as total_edits,
            COUNT(DISTINCT jeh.user_id) as unique_editors,
            MAX(jeh.edit_timestamp) as last_edit_time,
            (SELECT username FROM job_edit_history jeh2 
             WHERE jeh2.job_id = j.id 
             ORDER BY jeh2.edit_timestamp DESC LIMIT 1) as last_editor
        FROM jobs j
        LEFT JOIN job_edit_history jeh ON j.id = jeh.job_id
        GROUP BY j.id, j.job_number, j.customer_name
    ");
    
    // ตรวจสอบและเพิ่มคอลัมน์ที่อาจขาดหายไปในตาราง jobs
    echo "<p>Checking jobs table structure...</p>";
    
    $columns = $pdo->query("SHOW COLUMNS FROM jobs")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('closing_time', $columns)) {
        echo "<p>Adding closing_time column to jobs table...</p>";
        $pdo->exec("ALTER TABLE jobs ADD COLUMN closing_time TIME");
    }
    
    if (!in_array('depositor_name', $columns)) {
        echo "<p>Adding depositor_name column to jobs table...</p>";
        $pdo->exec("ALTER TABLE jobs ADD COLUMN depositor_name VARCHAR(255)");
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "<div style='color: green; font-weight: bold;'>";
    echo "<h3>✅ Installation completed successfully!</h3>";
    echo "<p>ระบบประวัติการแก้ไขได้ถูกติดตั้งเรียบร้อยแล้ว</p>";
    echo "</div>";
    
    // แสดงสถิติ
    echo "<h3>Database Statistics:</h3>";
    echo "<ul>";
    
    $count = $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn();
    echo "<li>Total Jobs: {$count}</li>";
    
    $count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<li>Total Users: {$count}</li>";
    
    $count = $pdo->query("SELECT COUNT(*) FROM job_edit_history")->fetchColumn();
    echo "<li>Total Edit History Records: {$count}</li>";
    
    $count = $pdo->query("SELECT COUNT(*) FROM return_users")->fetchColumn();
    echo "<li>Return Users: {$count}</li>";
    
    $count = $pdo->query("SELECT COUNT(*) FROM depositor_names")->fetchColumn();
    echo "<li>Depositor Names: {$count}</li>";
    
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>ไปที่ <a href='../public/job_list.php'>Job List</a> เพื่อดูรายการงาน</li>";
    echo "<li>คลิกที่ปุ่ม 'Edit' ในงานใดงานหนึ่งเพื่อทดสอบระบบแก้ไข</li>";
    echo "<li>ระบบจะบันทึกประวัติการแก้ไขทุกครั้งที่มีการเปลี่ยนแปลงข้อมูล</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollback();
    echo "<div style='color: red; font-weight: bold;'>";
    echo "<h3>❌ Installation failed!</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Edit History System Installation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        p { margin: 10px 0; }
        ul, ol { margin: 10px 0 10px 20px; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <hr>
    <p><strong>Installation completed.</strong> You can now use the edit_job.php system.</p>
    <p><a href="../public/dashboard.php">← Back to Dashboard</a></p>
</body>
</html>
