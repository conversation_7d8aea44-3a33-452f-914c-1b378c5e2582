-- ตารางสำหรับเก็บประวัติการแก้ไข job
CREATE TABLE job_edit_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    edit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_job_id (job_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (edit_timestamp)
);

-- ตารางสำหรับเก็บข้อมูลผู้ใช้ที่คืนตู้ (สำหรับ autocomplete)
CREATE TABLE return_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL
);

-- ตารางสำหรับเก็บข้อมูลชื่อผู้ฝากตู้ (สำหรับ autocomplete)
CREATE TABLE depositor_names (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL
);
