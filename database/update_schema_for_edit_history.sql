-- อัปเดตฐานข้อมูลสำหรับระบบประวัติการแก้ไข
-- รันไฟล์นี้เพื่อเพิ่มตารางที่จำเป็นสำหรับระบบ edit_job.php

-- ตารางสำหรับเก็บประวัติการแก้ไข job
CREATE TABLE IF NOT EXISTS job_edit_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    edit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_job_id (job_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (edit_timestamp)
);

-- ตารางสำหรับเก็บข้อมูลผู้ใช้ที่คืนตู้ (สำหรับ autocomplete)
CREATE TABLE IF NOT EXISTS return_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL
);

-- ตารางสำหรับเก็บข้อมูลชื่อผู้ฝากตู้ (สำหรับ autocomplete) - ถ้ายังไม่มี
CREATE TABLE IF NOT EXISTS depositor_names (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL
);

-- เพิ่มข้อมูลตัวอย่างสำหรับ return_users
INSERT IGNORE INTO return_users (name) VALUES 
('Admin'),
('User1'),
('User2');

-- เพิ่มข้อมูลตัวอย่างสำหรับ depositor_names
INSERT IGNORE INTO depositor_names (name) VALUES 
('บริษัท ABC จำกัด'),
('บริษัท XYZ จำกัด'),
('คุณสมชาย'),
('คุณสมหญิง');

-- อัปเดตตาราง jobs ถ้าจำเป็น (เพิ่มฟิลด์ที่อาจขาดหายไป)
-- ALTER TABLE jobs ADD COLUMN IF NOT EXISTS closing_time TIME;
-- ALTER TABLE jobs ADD COLUMN IF NOT EXISTS depositor_name VARCHAR(255);

-- สร้าง view สำหรับดูข้อมูลการแก้ไขแบบสรุป
CREATE OR REPLACE VIEW job_edit_summary AS
SELECT 
    j.id as job_id,
    j.job_number,
    j.customer_name,
    COUNT(jeh.id) as total_edits,
    COUNT(DISTINCT jeh.user_id) as unique_editors,
    MAX(jeh.edit_timestamp) as last_edit_time,
    (SELECT username FROM job_edit_history jeh2 
     WHERE jeh2.job_id = j.id 
     ORDER BY jeh2.edit_timestamp DESC LIMIT 1) as last_editor
FROM jobs j
LEFT JOIN job_edit_history jeh ON j.id = jeh.job_id
GROUP BY j.id, j.job_number, j.customer_name;

-- สร้าง stored procedure สำหรับดูประวัติการแก้ไขของ job
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetJobEditHistory(IN job_id_param INT)
BEGIN
    SELECT 
        jeh.*,
        u.username as editor_username,
        j.job_number
    FROM job_edit_history jeh
    JOIN users u ON u.id = jeh.user_id
    JOIN jobs j ON j.id = jeh.job_id
    WHERE jeh.job_id = job_id_param
    ORDER BY jeh.edit_timestamp DESC;
END //
DELIMITER ;

-- สร้าง trigger สำหรับบันทึกการแก้ไขอัตโนมัติ (ถ้าต้องการ)
-- DELIMITER //
-- CREATE TRIGGER IF NOT EXISTS jobs_audit_trigger
-- AFTER UPDATE ON jobs
-- FOR EACH ROW
-- BEGIN
--     -- บันทึกการเปลี่ยนแปลงที่สำคัญ
--     IF OLD.status != NEW.status THEN
--         INSERT INTO job_edit_history (job_id, user_id, username, field_name, old_value, new_value)
--         VALUES (NEW.id, @current_user_id, @current_username, 'status', OLD.status, NEW.status);
--     END IF;
-- END //
-- DELIMITER ;

-- แสดงข้อมูลสถิติ
SELECT 'Database update completed successfully' as status;
SELECT COUNT(*) as total_jobs FROM jobs;
SELECT COUNT(*) as total_users FROM users;
SELECT COUNT(*) as total_edit_history FROM job_edit_history;
