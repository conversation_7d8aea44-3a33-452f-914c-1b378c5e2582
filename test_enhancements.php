<?php
/**
 * Test script for system enhancements
 * This script tests the implemented features:
 * 1. Data visibility restrictions
 * 2. Admin-only delete permissions
 * 3. Modification date tracking
 * 4. Form validation
 */

session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Only allow admin to run tests
requireAdmin();

echo "<h1>System Enhancements Test Results</h1>";

// Test 1: Database Schema - Check if updated_at field exists
echo "<h2>1. Database Schema Test</h2>";
try {
    $stmt = $pdo->query("DESCRIBE jobs");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('updated_at', $columns)) {
        echo "<p style='color: green;'>✓ updated_at field exists in jobs table</p>";
    } else {
        echo "<p style='color: red;'>✗ updated_at field missing from jobs table</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking database schema: " . $e->getMessage() . "</p>";
}

// Test 2: Check if job_edit_history table exists
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM job_edit_history");
    echo "<p style='color: green;'>✓ job_edit_history table exists</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ job_edit_history table missing</p>";
}

// Test 3: Access Control Functions
echo "<h2>2. Access Control Functions Test</h2>";

// Test isAdmin function
if (function_exists('isAdmin')) {
    echo "<p style='color: green;'>✓ isAdmin() function exists</p>";
} else {
    echo "<p style='color: red;'>✗ isAdmin() function missing</p>";
}

// Test requireAdmin function
if (function_exists('requireAdmin')) {
    echo "<p style='color: green;'>✓ requireAdmin() function exists</p>";
} else {
    echo "<p style='color: red;'>✗ requireAdmin() function missing</p>";
}

// Test 4: File Permissions
echo "<h2>3. File Access Control Test</h2>";

$files_to_check = [
    'public/delete_job.php' => 'Admin-only job deletion',
    'public/user_delete.php' => 'Admin-only user deletion',
    'public/job_list.php' => 'User-specific job listing',
    'public/edit_job.php' => 'User-specific job editing',
    'public/view_job.php' => 'User-specific job viewing',
    'public/update_job.php' => 'User-specific job updating'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ {$description} file exists: {$file}</p>";
    } else {
        echo "<p style='color: red;'>✗ {$description} file missing: {$file}</p>";
    }
}

// Test 5: Form Validation
echo "<h2>4. Form Validation Test</h2>";

// Check if validation function exists in create_job.php
$create_job_content = file_get_contents('public/create_job.php');
if (strpos($create_job_content, 'function validateJobForm') !== false) {
    echo "<p style='color: green;'>✓ validateJobForm() function exists in create_job.php</p>";
} else {
    echo "<p style='color: red;'>✗ validateJobForm() function missing from create_job.php</p>";
}

// Check for CSRF token generation
if (strpos($create_job_content, 'generateCSRFToken()') !== false) {
    echo "<p style='color: green;'>✓ CSRF token generation found in create_job.php</p>";
} else {
    echo "<p style='color: red;'>✗ CSRF token generation missing from create_job.php</p>";
}

// Check for required field validation
if (strpos($create_job_content, 'required') !== false) {
    echo "<p style='color: green;'>✓ Required field validation found in create_job.php</p>";
} else {
    echo "<p style='color: red;'>✗ Required field validation missing from create_job.php</p>";
}

// Test 6: Database Migration
echo "<h2>5. Database Migration Test</h2>";

if (file_exists('database/migration_add_updated_at.sql')) {
    echo "<p style='color: green;'>✓ Database migration script exists</p>";
} else {
    echo "<p style='color: red;'>✗ Database migration script missing</p>";
}

// Test 7: Sample Data Test
echo "<h2>6. Sample Data Test</h2>";

try {
    // Check if there are any jobs in the system
    $stmt = $pdo->query("SELECT COUNT(*) FROM jobs");
    $job_count = $stmt->fetchColumn();
    echo "<p>Total jobs in system: {$job_count}</p>";
    
    // Check if there are any users in the system
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $user_count = $stmt->fetchColumn();
    echo "<p>Total users in system: {$user_count}</p>";
    
    // Check if there are any edit history records
    $stmt = $pdo->query("SELECT COUNT(*) FROM job_edit_history");
    $history_count = $stmt->fetchColumn();
    echo "<p>Total edit history records: {$history_count}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking sample data: " . $e->getMessage() . "</p>";
}

echo "<h2>7. Security Features Summary</h2>";
echo "<ul>";
echo "<li><strong>Data Visibility:</strong> Users can only view/edit their own jobs (except admins)</li>";
echo "<li><strong>Admin Permissions:</strong> Only admins can delete jobs and users</li>";
echo "<li><strong>Modification Tracking:</strong> updated_at field automatically tracks when records are modified</li>";
echo "<li><strong>Form Validation:</strong> Comprehensive client-side and server-side validation</li>";
echo "<li><strong>CSRF Protection:</strong> Forms protected against cross-site request forgery</li>";
echo "<li><strong>Edit History:</strong> All job modifications are logged with user details</li>";
echo "</ul>";

echo "<h2>8. Next Steps</h2>";
echo "<ol>";
echo "<li>Run the database migration script: <code>database/migration_add_updated_at.sql</code></li>";
echo "<li>Test the system with different user roles (admin vs regular user)</li>";
echo "<li>Verify that form validation works correctly</li>";
echo "<li>Test the delete functionality (admin only)</li>";
echo "<li>Check that edit history is being recorded properly</li>";
echo "</ol>";

echo "<p><a href='public/dashboard.php'>Return to Dashboard</a></p>";
?>
