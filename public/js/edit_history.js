/**
 * JavaScript สำหรับจัดการประวัติการแก้ไข Job
 * ใช้ร่วมกับ edit_job.php และ API
 */

class JobEditHistory {
    constructor(jobId) {
        this.jobId = jobId;
        this.apiUrl = 'api/job_edit_history.php';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadHistory();
    }

    bindEvents() {
        // เมื่อมีการเปลี่ยนแปลงในฟอร์ม
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('input', (e) => {
                this.markAsChanged();
            });

            form.addEventListener('submit', (e) => {
                this.onFormSubmit(e);
            });
        }

        // ปุ่มโหลดประวัติเพิ่มเติม
        const loadMoreBtn = document.getElementById('load-more-history');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreHistory();
            });
        }

        // ปุ่มรีเฟรชประวัติ
        const refreshBtn = document.getElementById('refresh-history');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshHistory();
            });
        }
    }

    /**
     * โหลดประวัติการแก้ไข
     */
    async loadHistory(offset = 0, limit = 20) {
        try {
            const response = await fetch(`${this.apiUrl}?job_id=${this.jobId}&offset=${offset}&limit=${limit}`);
            const data = await response.json();

            if (data.success) {
                this.renderHistory(data.history, offset === 0);
                this.renderStatistics(data.statistics);
                this.renderEditors(data.editors);
                
                // แสดง/ซ่อนปุ่มโหลดเพิ่มเติม
                const loadMoreBtn = document.getElementById('load-more-history');
                if (loadMoreBtn) {
                    loadMoreBtn.style.display = data.pagination.has_more ? 'block' : 'none';
                }
            } else {
                this.showError('ไม่สามารถโหลดประวัติการแก้ไขได้');
            }
        } catch (error) {
            console.error('Error loading history:', error);
            this.showError('เกิดข้อผิดพลาดในการโหลดข้อมูล');
        }
    }

    /**
     * โหลดประวัติเพิ่มเติม
     */
    async loadMoreHistory() {
        const historyContainer = document.querySelector('.edit-history');
        const currentItems = historyContainer.querySelectorAll('.history-item').length;
        await this.loadHistory(currentItems, 20);
    }

    /**
     * รีเฟรชประวัติ
     */
    async refreshHistory() {
        await this.loadHistory(0, 20);
    }

    /**
     * แสดงประวัติการแก้ไข
     */
    renderHistory(history, replace = true) {
        const container = document.querySelector('.edit-history');
        if (!container) return;

        if (replace) {
            container.innerHTML = '';
        }

        if (history.length === 0 && replace) {
            container.innerHTML = '<p class="text-muted">ยังไม่มีประวัติการแก้ไข</p>';
            return;
        }

        history.forEach(item => {
            const historyItem = this.createHistoryItem(item);
            container.appendChild(historyItem);
        });
    }

    /**
     * สร้าง HTML element สำหรับประวัติแต่ละรายการ
     */
    createHistoryItem(item) {
        const div = document.createElement('div');
        div.className = 'history-item';
        
        const timestamp = new Date(item.edit_timestamp).toLocaleString('th-TH');
        
        div.innerHTML = `
            <small class="text-muted">${timestamp}</small>
            <div>
                <strong>${this.escapeHtml(item.editor_username)}</strong>
                แก้ไข <em>${this.escapeHtml(item.field_name)}</em>
            </div>
            ${item.old_value !== item.new_value ? `
                <div class="small">
                    <span class="text-danger">เดิม:</span> 
                    ${this.escapeHtml(item.old_value || '(ว่าง)')}<br>
                    <span class="text-success">ใหม่:</span> 
                    ${this.escapeHtml(item.new_value || '(ว่าง)')}
                </div>
            ` : ''}
        `;

        return div;
    }

    /**
     * แสดงสถิติการแก้ไข
     */
    renderStatistics(stats) {
        const container = document.querySelector('.edit-statistics');
        if (!container || !stats) return;

        container.innerHTML = `
            <p><strong>จำนวนการแก้ไข:</strong> ${stats.total_edits || 0} ครั้ง</p>
            <p><strong>จำนวนผู้แก้ไข:</strong> ${stats.unique_editors || 0} คน</p>
            ${stats.first_edit ? `<p><strong>แก้ไขครั้งแรก:</strong> ${new Date(stats.first_edit).toLocaleString('th-TH')}</p>` : ''}
            ${stats.last_edit ? `<p><strong>แก้ไขล่าสุด:</strong> ${new Date(stats.last_edit).toLocaleString('th-TH')}</p>` : ''}
        `;
    }

    /**
     * แสดงรายชื่อผู้แก้ไข
     */
    renderEditors(editors) {
        const container = document.querySelector('.edit-editors');
        if (!container || !editors || editors.length === 0) return;

        const editorsList = editors.map(editor => 
            `<li><i class="fas fa-user"></i> ${this.escapeHtml(editor.username)} (${editor.edit_count} ครั้ง)</li>`
        ).join('');

        container.innerHTML = `
            <p><strong>ผู้แก้ไข:</strong></p>
            <ul class="list-unstyled">${editorsList}</ul>
        `;
    }

    /**
     * บันทึกประวัติการแก้ไขผ่าน API
     */
    async recordEdit(fieldName, oldValue, newValue) {
        if (oldValue === newValue) return;

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    job_id: this.jobId,
                    field_name: fieldName,
                    old_value: oldValue,
                    new_value: newValue
                })
            });

            const data = await response.json();
            if (!data.success) {
                console.error('Failed to record edit:', data.error);
            }
        } catch (error) {
            console.error('Error recording edit:', error);
        }
    }

    /**
     * เมื่อมีการส่งฟอร์ม
     */
    onFormSubmit(event) {
        // ล้าง draft
        this.clearDraft();
        
        // แสดงการโหลด
        this.showLoading(true);
        
        // รีเฟรชประวัติหลังจากบันทึกเสร็จ
        setTimeout(() => {
            this.refreshHistory();
            this.showLoading(false);
        }, 1000);
    }

    /**
     * ทำเครื่องหมายว่ามีการเปลี่ยนแปลง
     */
    markAsChanged() {
        window.hasChanges = true;
        
        // เปลี่ยนสีปุ่มบันทึก
        const saveBtn = document.querySelector('button[type="submit"]');
        if (saveBtn) {
            saveBtn.classList.add('btn-warning');
            saveBtn.classList.remove('btn-primary');
        }
    }

    /**
     * ล้างข้อมูล draft
     */
    clearDraft() {
        localStorage.removeItem(`job_edit_draft_${this.jobId}`);
        window.hasChanges = false;
    }

    /**
     * แสดง/ซ่อนการโหลด
     */
    showLoading(show) {
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * แสดงข้อผิดพลาด
     */
    showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.content');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
        }
    }

    /**
     * Escape HTML เพื่อป้องกัน XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// เริ่มต้นใช้งานเมื่อหน้าเว็บโหลดเสร็จ
document.addEventListener('DOMContentLoaded', function() {
    // ดึง job ID จาก URL หรือ element
    const jobId = new URLSearchParams(window.location.search).get('id');
    
    if (jobId) {
        window.jobEditHistory = new JobEditHistory(jobId);
    }
});

// Export สำหรับใช้งานใน module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JobEditHistory;
}
