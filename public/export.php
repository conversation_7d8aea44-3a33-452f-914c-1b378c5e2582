<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบประเภทการส่งออก
$export_type = $_GET['type'] ?? 'csv';

// สร้างคำสั่ง SQL การกรอง
$where = [];
$params = [];

// Filter by status
if (!empty($_GET["status"])) {
    $where[] = "status = ?";
    $params[] = $_GET["status"];
}

// Filter by user
if (!empty($_GET["user"])) {
    $where[] = "user_id = ?";
    $params[] = $_GET["user"];
}

// Filter by date range
if (!empty($_GET["start_date"])) {
    $where[] = "job_date >= ?";
    $params[] = $_GET["start_date"];
}
if (!empty($_GET["end_date"])) {
    $where[] = "job_date <= ?";
    $params[] = $_GET["end_date"];
}

// Build query
$sql = "SELECT jobs.*, users.username FROM jobs JOIN users ON users.id = jobs.user_id";
if ($where) $sql .= " WHERE " . implode(" AND ", $where);
$sql .= " ORDER BY job_date DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$data = $stmt->fetchAll();

// ส่งออกข้อมูลตามประเภท
if ($export_type == 'pdf') {
    // สร้าง HTML PDF
    $html = '<h1>Container System Report</h1>';
    $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
    $html .= '<tr style="background-color: #f2f2f2;">';
    $html .= '<th>Customer</th><th>Job Title</th><th>Booking/BL</th><th>Container No.</th><th>Size</th><th>Container Type</th><th>Job Date</th><th>First Return</th><th>Closing Date</th><th>Return Place</th><th>Deposit Date</th><th>Deposit Place</th><th>Final Return</th><th>Return User</th><th>Status</th><th>Service Fee</th><th>User</th>';
    $html .= '</tr>';
    
    foreach ($data as $row) {
        $html .= '<tr>';
        $html .= '<td>' . htmlspecialchars($row["customer_name"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["job_title"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["booking_bl"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["container_no"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["depositor_name"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["container_size"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["container_type"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["job_date"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["first_return_date"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["closing_date"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["return_place"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["deposit_date"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["deposit_place"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["final_return_date"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["return_user"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["status"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["service_fee"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["username"] ?? '') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["created_at"] ?? '') . '</td>';
        $html .= '</tr>';
    }
    
    $html .= '</table>';
    
    // ส่งออกเป็น PDF
    exportToPDF($html, 'container_report.pdf');
} else {
    // เตรียมข้อมูล CSV
    $csv_data = [];
    foreach ($data as $row) {
        $csv_data[] = [
            'Customer' => $row['customer_name'] ?? '',
            'Job Title' => $row['job_title'] ?? '',
            'Booking/BL' => $row['booking_bl'] ?? '',
            'Container No' => $row['container_no'] ?? '',
            'Depositor Name' => $row['depositor_name'] ?? '',
            'Size' => $row['container_size'] ?? '',
            'Container Type' => $row['container_type'] ?? '',
            'Job Date' => $row['job_date'] ?? '',
            'First Return' => $row['first_return_date'] ?? '',
            'Closing Date' => $row['closing_date'] ?? '',
            'Return Place' => $row['return_place'] ?? '',
            'Deposit Date' => $row['deposit_date'] ?? '',
            'Deposit Place' => $row['deposit_place'] ?? '',
            'Final Return' => $row['final_return_date'] ?? '',
            'Return User' => $row['return_user'] ?? '',
            'Status' => $row['status'] ?? '',
            'Service Fee' => $row['service_fee'] ?? '',
            'User' => $row['username'] ?? '',
            'Created At' => $row['created_at'] ?? ''
        ];
    }
    
    // ส่งออกเป็น CSV
    exportToCSV($csv_data, 'container_report.csv');
}
