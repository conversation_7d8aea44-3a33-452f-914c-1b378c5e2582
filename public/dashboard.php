<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการล็อกอิน
requireLogin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Dashboard - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">Container System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
                </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav ms-auto">
                <?php if (isset($_SESSION["username"])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </li>
                <?php else: ?>
                <li class="nav-item">
                    <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block">
            <div class="sidebar p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="job_list.php">
                            <i class="fas fa-list"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create_job.php">
                            <i class="fas fa-plus"></i> Create Job
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user_list.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <div class="col-md-9 col-lg-10">
            <div class="content">
                <h2>Dashboard</h2>
                
                <div class="row">
                    <?php
                    // ตรวจสอบสิทธิ์ผู้ใช้
                    $isAdmin = ($_SESSION["role"] === 'admin');

                    if ($isAdmin) {
                        // Admin เห็นข้อมูลทั้งหมด
                        $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM jobs GROUP BY status");
                        $statusCounts = [];
                        while ($row = $stmt->fetch()) {
                            $statusCounts[$row['status']] = $row['count'];
                        }

                        // Get user's job counts for "My Jobs" card
                        $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM jobs WHERE user_id = ? GROUP BY status");
                        $stmt->execute([$_SESSION["user_id"]]);
                        $userStatusCounts = [];
                        while ($row = $stmt->fetch()) {
                            $userStatusCounts[$row['status']] = $row['count'];
                        }
                    } else {
                        // User ทั่วไปเห็นเฉพาะข้อมูลของตัวเอง
                        $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM jobs WHERE user_id = ? GROUP BY status");
                        $stmt->execute([$_SESSION["user_id"]]);
                        $statusCounts = [];
                        while ($row = $stmt->fetch()) {
                            $statusCounts[$row['status']] = $row['count'];
                        }
                        $userStatusCounts = $statusCounts; // สำหรับ user ทั่วไป ข้อมูลเหมือนกัน
                    }
                    ?>
                    
                    <div class="col-md-6 col-lg-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title"><?= $isAdmin ? 'Total Jobs (All Users)' : 'My Total Jobs' ?></h5>
                                <p class="card-text display-4">
                                    <?= array_sum($statusCounts) ?>
                                </p>
                                <a href="job_list.php" class="text-white"><?= $isAdmin ? 'View All Jobs' : 'View My Jobs' ?></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title"><?= $isAdmin ? 'Completed Jobs (All)' : 'My Completed Jobs' ?></h5>
                                <p class="card-text display-4">
                                    <?= $statusCounts['Done'] ?? 0 ?>
                                </p>
                                <a href="job_list.php?status=Done" class="text-white">View Completed Jobs</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h5 class="card-title"><?= $isAdmin ? 'Pending Jobs (All)' : 'My Pending Jobs' ?></h5>
                                <p class="card-text display-4">
                                    <?= $statusCounts['Pending'] ?? 0 ?>
                                </p>
                                <a href="job_list.php?status=Pending" class="text-dark">View Pending Jobs</a>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($isAdmin): ?>
                    <div class="col-md-6 col-lg-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">My Personal Jobs</h5>
                                <p class="card-text display-4">
                                    <?= array_sum($userStatusCounts) ?>
                                </p>
                                <a href="job_list.php?user_id=<?= $_SESSION['user_id'] ?>" class="text-white">View My Personal Jobs</a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Admin Statistics -->
                <?php if ($isAdmin): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <i class="fas fa-chart-line"></i> <strong>Admin Overview - System Statistics</strong>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    // Get total users
                                    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
                                    $totalUsers = $stmt->fetchColumn();

                                    // Get active users (users with jobs)
                                    $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) as active_users FROM jobs");
                                    $activeUsers = $stmt->fetchColumn();

                                    // Get jobs created today
                                    $stmt = $pdo->query("SELECT COUNT(*) as today_jobs FROM jobs WHERE DATE(created_at) = CURDATE()");
                                    $todayJobs = $stmt->fetchColumn();

                                    // Get jobs created this week
                                    $stmt = $pdo->query("SELECT COUNT(*) as week_jobs FROM jobs WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)");
                                    $weekJobs = $stmt->fetchColumn();
                                    ?>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-primary"><?= $totalUsers ?></h5>
                                            <small class="text-muted">Total Users</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-success"><?= $activeUsers ?></h5>
                                            <small class="text-muted">Active Users</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-warning"><?= $todayJobs ?></h5>
                                            <small class="text-muted">Jobs Today</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-info"><?= $weekJobs ?></h5>
                                            <small class="text-muted">Jobs This Week</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                </div>

                <!-- ตู้เปล่า Pending -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-box"></i> ตู้เปล่า - สถานะ Pending <?= $isAdmin ? '(ทุกคน)' : '(ของฉัน)' ?>
                            </div>
                            <div class="card-body">
                                <!-- เล่ม CSS เฉพาะเจาะจงกว่า -->
                                <style>
                                    /* ใช้เฉพาะเจาะจงมากกว่าเพื่อทำให้ความเข้มของสีแดงมีความหลากหลาย */
                                    table.table tr.bg-danger-50 { background-color: rgba(220, 53, 69, 0.5) !important; }
                                    table.table tr.bg-danger-55 { background-color: rgba(220, 53, 69, 0.55) !important; }
                                    table.table tr.bg-danger-60 { background-color: rgba(220, 53, 69, 0.6) !important; }
                                    table.table tr.bg-danger-65 { background-color: rgba(220, 53, 69, 0.65) !important; }
                                    table.table tr.bg-danger-70 { background-color: rgba(220, 53, 69, 0.7) !important; }
                                    table.table tr.bg-danger-75 { background-color: rgba(220, 53, 69, 0.75) !important; }
                                    table.table tr.bg-danger-80 { background-color: rgba(220, 53, 69, 0.8) !important; }
                                    table.table tr.bg-danger-85 { background-color: rgba(220, 53, 69, 0.85) !important; }
                                    table.table tr.bg-danger-90 { background-color: rgba(220, 53, 69, 0.9) !important; }
                                    table.table tr.bg-danger-95 { background-color: rgba(220, 53, 69, 0.95) !important; }
                                    
                                    /* ยกเลิกการใช้ striped ในตาราง */
                                    table.table-striped tr:nth-of-type(odd) { background-color: transparent !important; }
                                </style>
                                
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Container No.</th>
                                                <th>Size</th>
                                                <th>ชื่อคนฝากตู้</th>
                                                <th>สถานที่คืนตู้</th>
                                                <th>Closing Date</th>
                                                <th>Closing Time</th>
                                                <th>วันที่ฝากตู้</th>
                                                <th>สถานที่ฝากตู้</th>
                                                <th>จำนวนวันฝาก</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // แสดงข้อมูลตาม user
                                            if ($isAdmin) {
                                                $stmt = $pdo->query("SELECT * FROM jobs WHERE container_type = 'เปล่า' AND status = 'Pending' ORDER BY deposit_date ASC");
                                            } else {
                                                $stmt = $pdo->prepare("SELECT * FROM jobs WHERE container_type = 'เปล่า' AND status = 'Pending' AND user_id = ? ORDER BY deposit_date ASC");
                                                $stmt->execute([$_SESSION["user_id"]]);
                                            }
                                            while ($row = $stmt->fetch()) {
                                                $days = 0;
                                                $rowClass = '';
                                                
                                                if (!empty($row['deposit_date'])) {
                                                    // คำนวณจำนวนวันระหว่างวันฝาก
                                                    $deposit_timestamp = strtotime($row['deposit_date']);
                                                    $today_timestamp = strtotime(date('Y-m-d'));
                                                    $diff_seconds = $today_timestamp - $deposit_timestamp;
                                                    $days = floor($diff_seconds / (60 * 60 * 24));
                                                    
                                                    // กำหนดคลาสตามจำนวนวัน
                                                    if ($days >= 2) {
                                                        // คำนวณความเข้มของแดง
                                                        $intensity = min(95, 50 + ($days * 5));
                                                        // ปัดเศษให้เป็น 5 หน่วย (50, 55, 60, ...)
                                                        $intensity = ceil($intensity / 5) * 5;
                                                        $rowClass = "class='bg-danger-{$intensity}'";
                                                    }
                                                }
                                                
                                                echo "<tr $rowClass>";
                                                echo "<td>" . htmlspecialchars($row['container_no'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['container_size'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['depositor_name'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['return_place'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['closing_date'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['closing_time'] ?? 'N/A') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['deposit_date'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['deposit_place'] ?? '') . "</td>";
                                                echo "<td>" . ($days > 0 ? $days . ' days' : 'N/A') . "</td>";
                                                echo "<td>";
                                                echo "<a href='update_job.php?id=" . htmlspecialchars($row['id'] ?? '') . "' class='btn btn-warning btn-sm'>Update</a>";
                                                echo "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ตู้หนัก Pending -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-box-open"></i> ตู้หนัก - สถานะ Pending <?= $isAdmin ? '(ทุกคน)' : '(ของฉัน)' ?>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Container No.</th>
                                                <th>Size</th>
                                                <th>ชื่อคนฝากตู้</th>
                                                <th>สถานที่คืนตู้</th>
                                                <th>Closing Date</th>
                                                <th>Closing Time</th>
                                                <th>วันที่ฝากตู้</th>
                                                <th>สถานที่ฝากตู้</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // แสดงข้อมูลตาม user
                                            if ($isAdmin) {
                                                $stmt = $pdo->query("SELECT * FROM jobs WHERE container_type = 'หนัก' AND status = 'Pending' ORDER BY deposit_date ASC");
                                            } else {
                                                $stmt = $pdo->prepare("SELECT * FROM jobs WHERE container_type = 'หนัก' AND status = 'Pending' AND user_id = ? ORDER BY deposit_date ASC");
                                                $stmt->execute([$_SESSION["user_id"]]);
                                            }
                                            while ($row = $stmt->fetch()) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($row['container_no'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['container_size'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['depositor_name'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['return_place'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['closing_date'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['closing_time'] ?? 'N/A') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['deposit_date'] ?? '') . "</td>";
                                                echo "<td>" . htmlspecialchars($row['deposit_place'] ?? '') . "</td>";
                                                echo "<td>";
                                                echo "<a href='update_job.php?id=" . htmlspecialchars($row['id'] ?? '') . "' class='btn btn-warning btn-sm'>Update</a>";
                                                echo "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

<div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-chart-pie"></i> Job Status Distribution
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-list"></i> Recent Jobs <?= $isAdmin ? '(ทุกคน)' : '(ของฉัน)' ?>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Container No.</th>
                                                <th>Customer</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // แสดงข้อมูลตาม user
                                            if ($isAdmin) {
                                                $stmt = $pdo->query("SELECT * FROM jobs ORDER BY job_date DESC LIMIT 5");
                                            } else {
                                                $stmt = $pdo->prepare("SELECT * FROM jobs WHERE user_id = ? ORDER BY job_date DESC LIMIT 5");
                                                $stmt->execute([$_SESSION["user_id"]]);
                                            }
                                            while ($row = $stmt->fetch()) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($row['container_no']) . "</td>";
                                                echo "<td>" . htmlspecialchars($row['customer_name']) . "</td>";
                                                echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                                                echo "<td>";
                                                echo "<a href='view_job.php?id=" . htmlspecialchars($row['id']) . "' class='btn btn-primary btn-sm'>View</a>";
                                                echo "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
// Status chart
const statusCtx = document.getElementById('statusChart');
new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: ['Pending', 'Done'],
        datasets: [{
            data: [<?= $statusCounts['Pending'] ?? 0 ?>, <?= $statusCounts['Done'] ?? 0 ?>],
            backgroundColor: ['#ffc107', '#28a745']
        }]
    }
});
</script>

<?php include '../includes/footer.php'; ?>
