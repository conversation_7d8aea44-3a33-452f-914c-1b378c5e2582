<?php
/**
 * API สำหรับดึงข้อมูลประวัติการแก้ไข Job
 * ใช้สำหรับ AJAX calls หรือ API integration
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$job_id = $_GET['job_id'] ?? null;

try {
    switch ($method) {
        case 'GET':
            if ($job_id) {
                // ดึงประวัติการแก้ไขของ job เฉพาะ
                getJobEditHistory($pdo, $job_id);
            } else {
                // ดึงสรุปประวัติการแก้ไขทั้งหมด
                getEditHistorySummary($pdo);
            }
            break;
            
        case 'POST':
            // บันทึกประวัติการแก้ไขใหม่ (สำหรับการใช้งานพิเศษ)
            addEditHistory($pdo);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * ดึงประวัติการแก้ไขของ job เฉพาะ
 */
function getJobEditHistory($pdo, $job_id) {
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    // ตรวจสอบว่า job มีอยู่จริง
    $stmt = $pdo->prepare("SELECT id, job_number FROM jobs WHERE id = ?");
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        http_response_code(404);
        echo json_encode(['error' => 'Job not found']);
        return;
    }
    
    // ดึงประวัติการแก้ไข
    $stmt = $pdo->prepare("
        SELECT 
            jeh.*,
            u.username as editor_username
        FROM job_edit_history jeh
        JOIN users u ON u.id = jeh.user_id
        WHERE jeh.job_id = ?
        ORDER BY jeh.edit_timestamp DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$job_id, $limit, $offset]);
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // นับจำนวนประวัติทั้งหมด
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM job_edit_history WHERE job_id = ?");
    $stmt->execute([$job_id]);
    $total_count = $stmt->fetchColumn();
    
    // สถิติการแก้ไข
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_edits,
            COUNT(DISTINCT user_id) as unique_editors,
            MIN(edit_timestamp) as first_edit,
            MAX(edit_timestamp) as last_edit
        FROM job_edit_history 
        WHERE job_id = ?
    ");
    $stmt->execute([$job_id]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // ผู้แก้ไขและจำนวนครั้ง
    $stmt = $pdo->prepare("
        SELECT 
            username,
            COUNT(*) as edit_count
        FROM job_edit_history jeh
        JOIN users u ON u.id = jeh.user_id
        WHERE jeh.job_id = ?
        GROUP BY jeh.user_id, username
        ORDER BY edit_count DESC
    ");
    $stmt->execute([$job_id]);
    $editors = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'job' => $job,
        'history' => $history,
        'pagination' => [
            'total' => $total_count,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total_count
        ],
        'statistics' => $stats,
        'editors' => $editors
    ]);
}

/**
 * ดึงสรุปประวัติการแก้ไขทั้งหมด
 */
function getEditHistorySummary($pdo) {
    $limit = $_GET['limit'] ?? 20;
    $offset = $_GET['offset'] ?? 0;
    
    // ดึงข้อมูลจาก view
    $stmt = $pdo->prepare("
        SELECT * FROM job_edit_summary
        ORDER BY last_edit_time DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // สถิติรวม
    $stats = $pdo->query("
        SELECT 
            COUNT(DISTINCT job_id) as total_jobs_edited,
            COUNT(*) as total_edits,
            COUNT(DISTINCT user_id) as total_editors,
            DATE(MIN(edit_timestamp)) as first_edit_date,
            DATE(MAX(edit_timestamp)) as last_edit_date
        FROM job_edit_history
    ")->fetch(PDO::FETCH_ASSOC);
    
    // การแก้ไขล่าสุด
    $recent_edits = $pdo->query("
        SELECT 
            jeh.*,
            u.username as editor_username,
            j.job_number
        FROM job_edit_history jeh
        JOIN users u ON u.id = jeh.user_id
        JOIN jobs j ON j.id = jeh.job_id
        ORDER BY jeh.edit_timestamp DESC
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'summary' => $summary,
        'statistics' => $stats,
        'recent_edits' => $recent_edits,
        'pagination' => [
            'limit' => $limit,
            'offset' => $offset
        ]
    ]);
}

/**
 * บันทึกประวัติการแก้ไขใหม่
 */
function addEditHistory($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $required_fields = ['job_id', 'field_name', 'old_value', 'new_value'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            return;
        }
    }
    
    // ตรวจสอบว่า job มีอยู่จริง
    $stmt = $pdo->prepare("SELECT id FROM jobs WHERE id = ?");
    $stmt->execute([$input['job_id']]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Job not found']);
        return;
    }
    
    // บันทึกประวัติ
    $stmt = $pdo->prepare("
        INSERT INTO job_edit_history 
        (job_id, user_id, username, field_name, old_value, new_value, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $input['job_id'],
        $_SESSION['user_id'],
        $_SESSION['username'],
        $input['field_name'],
        $input['old_value'],
        $input['new_value'],
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Edit history recorded successfully',
        'id' => $pdo->lastInsertId()
    ]);
}
?>
