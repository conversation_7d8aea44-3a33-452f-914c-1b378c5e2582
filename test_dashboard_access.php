<?php
/**
 * Test script for dashboard access control
 * This script tests that the dashboard shows appropriate data based on user role
 */

session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

// Only allow logged in users to run tests
requireLogin();

echo "<h1>Dashboard Access Control Test</h1>";
echo "<p><strong>Current User:</strong> " . htmlspecialchars($_SESSION['username']) . " (" . htmlspecialchars($_SESSION['role']) . ")</p>";

$isAdmin = ($_SESSION["role"] === 'admin');

echo "<h2>1. User Role Detection</h2>";
echo "<p>Is Admin: " . ($isAdmin ? "YES" : "NO") . "</p>";

echo "<h2>2. Data Visibility Test</h2>";

// Test job counts
if ($isAdmin) {
    // Admin should see all jobs
    $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM jobs GROUP BY status");
    echo "<p><strong>Admin View:</strong> Showing all jobs from all users</p>";
} else {
    // User should see only their jobs
    $stmt = $pdo->prepare("SELECT status, COUNT(*) as count FROM jobs WHERE user_id = ? GROUP BY status");
    $stmt->execute([$_SESSION["user_id"]]);
    echo "<p><strong>User View:</strong> Showing only jobs created by current user</p>";
}

$statusCounts = [];
while ($row = $stmt->fetch()) {
    $statusCounts[$row['status']] = $row['count'];
}

echo "<h3>Job Statistics:</h3>";
echo "<ul>";
echo "<li>Total Jobs: " . array_sum($statusCounts) . "</li>";
echo "<li>Pending Jobs: " . ($statusCounts['Pending'] ?? 0) . "</li>";
echo "<li>Completed Jobs: " . ($statusCounts['Done'] ?? 0) . "</li>";
echo "</ul>";

// Test container data visibility
echo "<h2>3. Container Data Test</h2>";

if ($isAdmin) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM jobs WHERE container_type = 'เปล่า' AND status = 'Pending'");
    echo "<p><strong>Admin View:</strong> Empty containers (all users)</p>";
} else {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM jobs WHERE container_type = 'เปล่า' AND status = 'Pending' AND user_id = ?");
    $stmt->execute([$_SESSION["user_id"]]);
    echo "<p><strong>User View:</strong> Empty containers (own jobs only)</p>";
}

$emptyContainers = $stmt->fetchColumn();
echo "<p>Empty Containers (Pending): " . $emptyContainers . "</p>";

if ($isAdmin) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM jobs WHERE container_type = 'หนัก' AND status = 'Pending'");
} else {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM jobs WHERE container_type = 'หนัก' AND status = 'Pending' AND user_id = ?");
    $stmt->execute([$_SESSION["user_id"]]);
}

$heavyContainers = $stmt->fetchColumn();
echo "<p>Heavy Containers (Pending): " . $heavyContainers . "</p>";

// Test recent jobs
echo "<h2>4. Recent Jobs Test</h2>";

if ($isAdmin) {
    $stmt = $pdo->query("SELECT * FROM jobs ORDER BY job_date DESC LIMIT 3");
    echo "<p><strong>Admin View:</strong> Recent jobs from all users</p>";
} else {
    $stmt = $pdo->prepare("SELECT * FROM jobs WHERE user_id = ? ORDER BY job_date DESC LIMIT 3");
    $stmt->execute([$_SESSION["user_id"]]);
    echo "<p><strong>User View:</strong> Recent jobs from current user only</p>";
}

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Container No</th><th>Customer</th><th>Status</th><th>Created By</th></tr>";

while ($row = $stmt->fetch()) {
    // Get username for the job
    $userStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $userStmt->execute([$row['user_id']]);
    $jobUser = $userStmt->fetchColumn();
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['container_no'] ?? 'N/A') . "</td>";
    echo "<td>" . htmlspecialchars($row['customer_name'] ?? 'N/A') . "</td>";
    echo "<td>" . htmlspecialchars($row['status'] ?? 'N/A') . "</td>";
    echo "<td>" . htmlspecialchars($jobUser ?? 'Unknown') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Admin-only statistics
if ($isAdmin) {
    echo "<h2>5. Admin-Only Statistics</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
    $totalUsers = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) as active_users FROM jobs");
    $activeUsers = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as today_jobs FROM jobs WHERE DATE(created_at) = CURDATE()");
    $todayJobs = $stmt->fetchColumn();
    
    echo "<ul>";
    echo "<li>Total Users in System: " . $totalUsers . "</li>";
    echo "<li>Active Users (with jobs): " . $activeUsers . "</li>";
    echo "<li>Jobs Created Today: " . $todayJobs . "</li>";
    echo "</ul>";
} else {
    echo "<h2>5. Admin-Only Statistics</h2>";
    echo "<p><em>Not available for regular users</em></p>";
}

echo "<h2>6. Navigation Access Test</h2>";
echo "<p>Users menu should be " . ($isAdmin ? "VISIBLE" : "HIDDEN") . " for " . $_SESSION['role'] . "</p>";

echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li><strong>Role-based data filtering:</strong> " . ($isAdmin ? "Admin sees all data" : "User sees only own data") . "</li>";
echo "<li><strong>Statistics accuracy:</strong> Numbers reflect appropriate data scope</li>";
echo "<li><strong>Navigation control:</strong> Menu items shown based on permissions</li>";
echo "</ul>";

echo "<p><a href='public/dashboard.php'>Go to Dashboard</a> | <a href='public/job_list.php'>Go to Job List</a></p>";
?>
