<?php
session_start();
require_once 'includes/functions.php';

echo "<h1>CSRF Token Test</h1>";

// Test 1: Generate CSRF token HTML
echo "<h2>1. Generate CSRF Token HTML</h2>";
echo "<pre>" . htmlspecialchars(generateCSRFToken()) . "</pre>";

// Test 2: Get CSRF token value
echo "<h2>2. Get CSRF Token Value</h2>";
echo "<pre>" . htmlspecialchars(getCSRFToken()) . "</pre>";

// Test 3: Session token
echo "<h2>3. Session Token</h2>";
echo "<pre>" . htmlspecialchars($_SESSION['csrf_token'] ?? 'NOT SET') . "</pre>";

// Test 4: Validation test
echo "<h2>4. Validation Test</h2>";
$token = getCSRFToken();
$valid = validateCSRFToken($token);
echo "<p>Token validation result: " . ($valid ? "VALID" : "INVALID") . "</p>";

// Test 5: Form test
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    echo "<h2>5. Form Submission Test</h2>";
    echo "<p>Submitted token: " . htmlspecialchars($_POST['csrf_token'] ?? 'MISSING') . "</p>";
    echo "<p>Session token: " . htmlspecialchars($_SESSION['csrf_token'] ?? 'MISSING') . "</p>";
    echo "<p>Validation result: " . (validateCSRFToken($_POST['csrf_token'] ?? '') ? "VALID" : "INVALID") . "</p>";
}
?>

<h2>Test Form</h2>
<form method="post">
    <?= generateCSRFToken() ?>
    <button type="submit">Test CSRF</button>
</form>

<p><a href="public/create_job.php">Go to Create Job</a></p>
