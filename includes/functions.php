<?php
// ฟังก์ชันช่วยต่างๆ

// สร้าง CSRF token
function generateCSRFToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($_SESSION['csrf_token']) . '">';
}

// ดึงค่า CSRF token
function getCSRFToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// ตรวจสอบ CSRF token
function validateCSRFToken($token) {
    if (empty($_SESSION['csrf_token']) || empty($token)) {
        error_log("CSRF token empty: Session token=" . (empty($_SESSION['csrf_token']) ? "empty" : "exists") . 
                  ", Submitted token=" . (empty($token) ? "empty" : "exists"));
        return false;
    }
    
    $result = hash_equals($_SESSION['csrf_token'], $token);
    if (!$result) {
        error_log("CSRF token mismatch: Expected {$_SESSION['csrf_token']}, got {$token}");
    }
    
    return $result;
}

// ฟังก์ชันส่งออกข้อมูลเป็น CSV
function exportToCSV($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // ส่งออก header
    if (!empty($data)) {
        fputcsv($output, array_keys($data[0]));
    }
    
    // ส่งออกข้อมูล
    foreach ($data as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
}

// ฟังก์ชันส่งออกข้อมูลเป็น PDF
function exportToPDF($html, $filename) {
    // ต้องติดตั้ง library เช่น TCPDF หรือ MPDF ก่อน
    // ตัวอย่างการใช้ MPDF
    require_once '../vendor/autoload.php';
    $mpdf = new \Mpdf\Mpdf();
    $mpdf->WriteHTML($html);
    $mpdf->Output($filename, 'D');
    exit;
}

// ฟังก์ชันแจ้งเตือนงานที่ใกล้ถึงกำหนด
function getUpcomingDeadlines($pdo, $days = 7) {
    $stmt = $pdo->prepare("SELECT * FROM jobs 
                          WHERE status = 'Pending' 
                          AND closing_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)");
    $stmt->execute([$days]);
    return $stmt->fetchAll();
}
